# Firebase Firestore Troubleshooting Guide

## Quick Fix Steps

### 1. Deploy Firestore Security Rules
```bash
# Install Firebase CLI (if not already installed)
npm install -g firebase-tools

# Login to Firebase
firebase login

# Set your project
firebase use aiebookwriter

# Deploy security rules
firebase deploy --only firestore
```

### 2. Verify Authentication Status
Open browser dev tools and run:
```javascript
// Check if user is authenticated
console.log('Current user:', firebase.auth().currentUser);
console.log('User ID:', firebase.auth().currentUser?.uid);
console.log('Email verified:', firebase.auth().currentUser?.emailVerified);
```

### 3. Test Firestore Connection
In your browser console:
```javascript
// Import the debug helper (add to your component temporarily)
import { debugAuth, testFirestoreConnection } from '@/lib/authDebug';

// Run diagnostics
debugAuth();
testFirestoreConnection();
```

## Common Issues & Solutions

### Issue 1: "Missing or insufficient permissions"
**Cause:** Firestore security rules are blocking the operation
**Solution:** Deploy the security rules using the commands above

### Issue 2: "User must be authenticated"
**Cause:** User is not properly authenticated or auth token is missing
**Solutions:**
1. Ensure user is logged in
2. Check email verification status
3. Try logging out and back in

### Issue 3: "Bad Request (400)"
**Cause:** Malformed request or authentication issues
**Solutions:**
1. Check network tab for detailed error messages
2. Verify Firebase project configuration
3. Ensure environment variables are correct

### Issue 4: Environment Variables
Verify your `.env` file contains:
```
VITE_FIREBASE_API_KEY=AIzaSyC1EPbhsqYAFPfsxBtOF1xIbU45RoA85RA
VITE_FIREBASE_AUTH_DOMAIN=aiebookwriter.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=aiebookwriter
VITE_FIREBASE_STORAGE_BUCKET=aiebookwriter.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=428756360026
VITE_FIREBASE_APP_ID=1:428756360026:web:a2a3f41d2ec3da25143163
VITE_FIREBASE_MEASUREMENT_ID=G-TZFXM9KSTH
```

## Security Rules Explanation

The deployed rules allow:
- ✅ Users to create projects for themselves
- ✅ Users to read their own projects
- ✅ Users to update their own projects
- ✅ Users to delete their own projects
- ❌ Users to access other users' projects
- ❌ Unauthenticated access

## Testing Checklist

1. [ ] User is logged in and authenticated
2. [ ] User's email is verified
3. [ ] Firestore security rules are deployed
4. [ ] Environment variables are correctly set
5. [ ] Network requests show proper authentication headers
6. [ ] Browser console shows no authentication errors

## Manual Testing

1. Log into your application
2. Generate a book outline
3. Check browser dev tools for any errors
4. Verify project appears in "Current Project" page

If issues persist, check the Firebase Console:
- Authentication: https://console.firebase.google.com/project/aiebookwriter/authentication
- Firestore: https://console.firebase.google.com/project/aiebookwriter/firestore
- Rules: https://console.firebase.google.com/project/aiebookwriter/firestore/rules