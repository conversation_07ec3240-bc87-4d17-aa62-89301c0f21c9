import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { generateOutline, generateChapterContent, generateBookTitles } from "./gemini";
import { OutlineGenerationParams, ContentGenerationParams, BookTitle, TargetAudience } from "@shared/types";

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes for eBook generation
  
  // New endpoint for generating book titles
  app.post("/api/generate-titles", async (req, res) => {
    try {
      const { userInput, generationParams } = req.body;
      
      if (!userInput || typeof userInput !== "string") {
        return res.status(400).json({ 
          message: "Invalid input. Please provide a valid 'userInput' string." 
        });
      }

      const titles = await generateBookTitles(userInput, generationParams);
      return res.json({ titles });
    } catch (error) {
      console.error("Error generating book titles:", error);
      return res.status(500).json({ 
        message: "Failed to generate book titles. Please try again." 
      });
    }
  });

  // Updated endpoint for generating outline (now accepts selectedTitle)
  app.post("/api/generate-outline", async (req, res) => {
    try {
      const { selectedTitle, userInput, generationParams } = req.body;
      
      // Use selected title as primary input, fallback to userInput
      const bookTitle = selectedTitle?.title || userInput;
      
      if (!bookTitle || typeof bookTitle !== "string") {
        return res.status(400).json({ 
          message: "Invalid input. Please provide a selected title or user input." 
        });
      }

      // Validate generation parameters
      const params: OutlineGenerationParams = generationParams || {
        maxChapters: 5,
        maxSubChapters: 3
      };

      // Add explicit validation for minimum values
      if (params.maxChapters < 5) {
        return res.status(400).json({ 
          message: "Invalid parameters. Minimum chapters allowed is 5." 
        });
      }

      if (params.maxSubChapters < 3) {
        return res.status(400).json({ 
          message: "Invalid parameters. Minimum sub-chapters allowed is 3." 
        });
      }

      const outline = await generateOutline(bookTitle, params);
      return res.json({ outline });
    } catch (error) {
      console.error("Error generating outline:", error);
      return res.status(500).json({ 
        message: "Failed to generate outline. Please try again." 
      });
    }
  });

  app.post("/api/generate-chapter", async (req, res) => {
    try {
      const { 
        subChapterTitle, 
        mainChapterTitle, 
        bookTopic, 
        generationParams 
      } = req.body;
      
      if (!subChapterTitle || !mainChapterTitle || !bookTopic) {
        return res.status(400).json({ 
          message: "Missing required fields. Please provide subChapterTitle, mainChapterTitle, and bookTopic." 
        });
      }

      // Default parameters if not provided
      const params: ContentGenerationParams = generationParams || {
        tone: 'professional',
        style: 'descriptive',
        language: 'intermediate'
      };

      const subChapterContent = await generateChapterContent(
        subChapterTitle,
        mainChapterTitle,
        bookTopic,
        params
      );
      
      return res.json({ subChapterContent });
    } catch (error) {
      console.error("Error generating chapter content:", error);
      return res.status(500).json({ 
        message: "Failed to generate chapter content. Please try again." 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
