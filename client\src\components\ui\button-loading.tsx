import * as React from "react";
import { <PERSON><PERSON>, ButtonProps } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { cn } from "@/lib/utils";
import { 
  BookOpenIcon, 
  PenToolIcon, 
  ListIcon, 
  ShieldCheckIcon,
  MailIcon,
  RefreshCwIcon,
  Loader2Icon 
} from "lucide-react";

interface ButtonLoadingProps extends ButtonProps {
  isLoading?: boolean;
  loadingText?: string;
  loadingType?: 
    | "titles" 
    | "outline" 
    | "content" 
    | "auth" 
    | "email" 
    | "general"
    | "regenerate";
  showProgress?: boolean;
  progress?: number;
  children: React.ReactNode;
}

const LoadingButton = React.forwardRef<HTMLButtonElement, ButtonLoadingProps>(
  ({ 
    isLoading = false,
    loadingText,
    loadingType = "general",
    showProgress = false,
    progress = 0,
    children,
    disabled,
    className,
    ...props 
  }, ref) => {
    // Respect user's motion preferences
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    // Context-specific loading configurations
    const loadingConfig = React.useMemo(() => {
      const configs = {
        titles: {
          icon: <BookOpenIcon className="w-4 h-4" />,
          text: loadingText || "Generating Titles",
          spinner: "particle" as const,
          description: "Creating compelling book titles..."
        },
        outline: {
          icon: <ListIcon className="w-4 h-4" />,
          text: loadingText || "Generating Outline",
          spinner: "glass" as const,
          description: "Building book structure..."
        },
        content: {
          icon: <PenToolIcon className="w-4 h-4" />,
          text: loadingText || "Generating Content",
          spinner: "gradient" as const,
          description: "Writing chapter content..."
        },
        auth: {
          icon: <ShieldCheckIcon className="w-4 h-4" />,
          text: loadingText || "Authenticating",
          spinner: "ripple" as const,
          description: "Securing your session..."
        },
        email: {
          icon: <MailIcon className="w-4 h-4" />,
          text: loadingText || "Sending Email",
          spinner: "glass" as const,
          description: "Delivering verification..."
        },
        regenerate: {
          icon: <RefreshCwIcon className="w-4 h-4" />,
          text: loadingText || "Regenerating",
          spinner: "gradient" as const,
          description: "Creating new content..."
        },
        general: {
          icon: <Loader2Icon className="w-4 h-4" />,
          text: loadingText || "Loading",
          spinner: "glass" as const,
          description: "Processing request..."
        }
      };
      
      return configs[loadingType] || configs.general;
    }, [loadingType, loadingText]);

    // Typewriter effect for loading text
    const [displayText, setDisplayText] = React.useState("");
    const [showCursor, setShowCursor] = React.useState(true);

    React.useEffect(() => {
      if (!isLoading || prefersReducedMotion) {
        setDisplayText(loadingConfig.text);
        return;
      }

      let currentIndex = 0;
      const targetText = loadingConfig.text;
      
      const typewriterInterval = setInterval(() => {
        if (currentIndex <= targetText.length) {
          setDisplayText(targetText.slice(0, currentIndex));
          currentIndex++;
        } else {
          clearInterval(typewriterInterval);
        }
      }, 50);

      return () => clearInterval(typewriterInterval);
    }, [isLoading, loadingConfig.text, prefersReducedMotion]);

    // Blinking cursor effect
    React.useEffect(() => {
      if (!isLoading || prefersReducedMotion) {
        setShowCursor(false);
        return;
      }

      const cursorInterval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);

      return () => clearInterval(cursorInterval);
    }, [isLoading, prefersReducedMotion]);

    const buttonContent = React.useMemo(() => {
      if (isLoading) {
        return (
          <div className="flex items-center justify-center space-x-2">
            <LoadingSpinner 
              size="sm" 
              variant={loadingConfig.spinner}
              showProgress={showProgress}
              progress={progress}
            />
            <span className="flex items-center">
              {prefersReducedMotion ? (
                loadingConfig.text
              ) : (
                <>
                  {displayText}
                  <span 
                    className={cn(
                      "ml-0.5 w-0.5 h-4 bg-current transition-opacity duration-100",
                      showCursor ? "opacity-100" : "opacity-0"
                    )}
                  />
                </>
              )}
            </span>
          </div>
        );
      }
      
      return children;
    }, [
      isLoading, 
      loadingConfig, 
      displayText, 
      showCursor, 
      children, 
      prefersReducedMotion,
      showProgress,
      progress
    ]);

    return (
      <Button
        ref={ref}
        disabled={disabled || isLoading}
        className={cn(
          "transition-all duration-300 ease-in-out",
          isLoading && "cursor-not-allowed",
          className
        )}
        {...props}
      >
        {buttonContent}
      </Button>
    );
  }
);

LoadingButton.displayName = "LoadingButton";

// Specialized button components for specific use cases

interface GenerateTitlesButtonProps extends Omit<ButtonLoadingProps, "loadingType"> {
  isGenerating?: boolean;
}

const GenerateTitlesButton = React.forwardRef<HTMLButtonElement, GenerateTitlesButtonProps>(
  ({ isGenerating, ...props }, ref) => {
    return (
      <LoadingButton
        ref={ref}
        isLoading={isGenerating}
        loadingType="titles"
        {...props}
      />
    );
  }
);

GenerateTitlesButton.displayName = "GenerateTitlesButton";

interface GenerateOutlineButtonProps extends Omit<ButtonLoadingProps, "loadingType"> {
  isGenerating?: boolean;
}

const GenerateOutlineButton = React.forwardRef<HTMLButtonElement, GenerateOutlineButtonProps>(
  ({ isGenerating, ...props }, ref) => {
    return (
      <LoadingButton
        ref={ref}
        isLoading={isGenerating}
        loadingType="outline"
        {...props}
      />
    );
  }
);

GenerateOutlineButton.displayName = "GenerateOutlineButton";

interface GenerateContentButtonProps extends Omit<ButtonLoadingProps, "loadingType"> {
  isGenerating?: boolean;
}

const GenerateContentButton = React.forwardRef<HTMLButtonElement, GenerateContentButtonProps>(
  ({ isGenerating, showProgress, progress, ...props }, ref) => {
    return (
      <LoadingButton
        ref={ref}
        isLoading={isGenerating}
        loadingType="content"
        showProgress={showProgress}
        progress={progress}
        {...props}
      />
    );
  }
);

GenerateContentButton.displayName = "GenerateContentButton";

interface AuthButtonProps extends Omit<ButtonLoadingProps, "loadingType"> {
  isAuthenticating?: boolean;
}

const AuthButton = React.forwardRef<HTMLButtonElement, AuthButtonProps>(
  ({ isAuthenticating, ...props }, ref) => {
    return (
      <LoadingButton
        ref={ref}
        isLoading={isAuthenticating}
        loadingType="auth"
        {...props}
      />
    );
  }
);

AuthButton.displayName = "AuthButton";

interface RegenerateButtonProps extends Omit<ButtonLoadingProps, "loadingType"> {
  isRegenerating?: boolean;
}

const RegenerateButton = React.forwardRef<HTMLButtonElement, RegenerateButtonProps>(
  ({ isRegenerating, ...props }, ref) => {
    return (
      <LoadingButton
        ref={ref}
        isLoading={isRegenerating}
        loadingType="regenerate"
        {...props}
      />
    );
  }
);

RegenerateButton.displayName = "RegenerateButton";

export {
  LoadingButton,
  GenerateTitlesButton,
  GenerateOutlineButton,
  GenerateContentButton,
  AuthButton,
  RegenerateButton
};