import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { useAppData } from "@/context/AppDataContext";
import { ProjectService } from "@/lib/projectService";
import { SavedProject } from "@shared/types";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { BookIcon, CalendarIcon, EditIcon, TrashIcon, PlayIcon, FileTextIcon } from "lucide-react";
import { useLocation } from "wouter";
import { format } from "date-fns";

interface ProjectCardProps {
  project: SavedProject;
  onContinue: (project: SavedProject) => void;
  onDelete: (projectId: string) => void;
  onRename: (projectId: string, newTitle: string) => void;
}

function ProjectCard({ project, onContinue, onDelete, onRename }: ProjectCardProps) {
  const [isRenaming, setIsRenaming] = useState(false);
  const [newTitle, setNewTitle] = useState(project.title);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);

  const handleRename = () => {
    if (newTitle.trim() && newTitle.trim() !== project.title) {
      onRename(project.id, newTitle.trim());
      setIsRenameDialogOpen(false);
    }
  };

  const getStatusColor = (status: SavedProject['status']) => {
    switch (status) {
      case 'outline_generated':
        return 'bg-blue-100 text-blue-800';
      case 'content_in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: SavedProject['status']) => {
    switch (status) {
      case 'outline_generated':
        return 'Outline Generated';
      case 'content_in_progress':
        return 'Content in Progress';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  };

  const getProgressInfo = () => {
    if (!project.generatedChapters) return null;
    
    const totalChapters = Object.keys(project.generatedChapters).length;
    const generatedCount = Object.values(project.generatedChapters).filter(Boolean).length;
    
    if (totalChapters === 0) return null;
    
    return `${generatedCount}/${totalChapters} chapters`;
  };

  return (
    <Card className="glass-card hover:shadow-lg transition-all duration-300">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold mb-2 line-clamp-2">
              {project.title}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground line-clamp-2">
              {project.topic}
            </CardDescription>
          </div>
          <Badge className={getStatusColor(project.status)}>
            {getStatusText(project.status)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center text-sm text-muted-foreground">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Created {format(project.createdAt, 'MMM dd, yyyy')}
          </div>
          
          <div className="flex items-center text-sm text-muted-foreground">
            <BookIcon className="h-4 w-4 mr-2" />
            {project.outline.chapters.length} chapters
          </div>
          
          {getProgressInfo() && (
            <div className="flex items-center text-sm text-muted-foreground">
              <FileTextIcon className="h-4 w-4 mr-2" />
              {getProgressInfo()}
            </div>
          )}
          
          {project.bookSummary && (
            <p className="text-sm text-muted-foreground line-clamp-3 mt-2">
              {project.bookSummary}
            </p>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <EditIcon className="h-4 w-4 mr-1" />
                Rename
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Rename Project</DialogTitle>
                <DialogDescription>
                  Enter a new title for your project.
                </DialogDescription>
              </DialogHeader>
              <Input
                value={newTitle}
                onChange={(e) => setNewTitle(e.target.value)}
                placeholder="Enter project title"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleRename();
                  }
                }}
              />
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsRenameDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleRename} disabled={!newTitle.trim() || newTitle.trim() === project.title}>
                  Rename
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                <TrashIcon className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Project</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete "{project.title}"? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDelete(project.id)}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
        
        <Button onClick={() => onContinue(project)} className="ml-auto">
          <PlayIcon className="h-4 w-4 mr-1" />
          Continue
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function CurrentProjectPage() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { setProjectToLoad } = useAppData();
  
  const [projects, setProjects] = useState<SavedProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load projects when component mounts
  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    if (!user?.uid) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const userProjects = await ProjectService.getUserProjects(user.uid);
      setProjects(userProjects);
    } catch (err) {
      console.error('Error loading projects:', err);
      setError('Failed to load projects');
      toast({
        title: "Error",
        description: "Failed to load your projects. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinueProject = (project: SavedProject) => {
    // Store project data in context for the AppPage to load
    setProjectToLoad(project);

    // Navigate to the create-ebook page
    setLocation('/create-ebook');

    toast({
      title: "Project loaded",
      description: `Continuing work on "${project.title}"`,
    });
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      await ProjectService.deleteProject(projectId);
      setProjects(projects.filter(p => p.id !== projectId));
      
      toast({
        title: "Project deleted",
        description: "The project has been deleted successfully.",
      });
    } catch (err) {
      console.error('Error deleting project:', err);
      toast({
        title: "Error",
        description: "Failed to delete the project. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleRenameProject = async (projectId: string, newTitle: string) => {
    try {
      await ProjectService.updateProjectTitle(projectId, newTitle);
      setProjects(projects.map(p => 
        p.id === projectId ? { ...p, title: newTitle } : p
      ));
      
      toast({
        title: "Project renamed",
        description: "The project title has been updated successfully.",
      });
    } catch (err) {
      console.error('Error renaming project:', err);
      toast({
        title: "Error",
        description: "Failed to rename the project. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="glass-card max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please log in to view your projects.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setLocation('/login')} className="w-full">
              Go to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <Card className="glass-card max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-destructive">Error</CardTitle>
              <CardDescription>{error}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button onClick={loadProjects} className="w-full">
                Try Again
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Your Projects</h1>
            <p className="text-muted-foreground">
              Manage and continue working on your book projects
            </p>
          </div>

          {/* Projects Grid */}
          {projects.length === 0 ? (
            <Card className="glass-card text-center py-12">
              <CardContent>
                <BookIcon className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <CardTitle className="text-xl mb-2">No Projects Yet</CardTitle>
                <CardDescription className="mb-6">
                  You haven't created any book projects yet. Start your first project to see it here.
                </CardDescription>
                <Button onClick={() => setLocation('/create-ebook')}>
                  Create Your First Project
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onContinue={handleContinueProject}
                  onDelete={handleDeleteProject}
                  onRename={handleRenameProject}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}