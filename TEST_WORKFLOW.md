# Workflow Test Plan

## Test: Premature Data Deletion Fix

### Before Fix (Issue):
1. User clicks "Start Writing" on saved idea
2. ❌ Idea immediately deleted from Firestore  
3. ❌ "Change Selection" button appears inappropriately
4. ❌ If user navigates away, data is lost forever

### After Fix (Expected):
1. User clicks "Start Writing" on saved idea  
2. ✅ Idea preserved in Firestore
3. ✅ Navigate to Create eBook page with title applied
4. ✅ "Change Selection" button hidden (projectContext = 'existing')
5. ✅ User can still navigate away safely - idea remains saved
6. ✅ Only when user clicks "Generate Outline":
   - Idea deleted from Firestore  
   - Converted to project
   - Toast: "Saved idea converted to project"

### Test Cases:

#### Test 1: Full Happy Path
- [ ] Save an idea from title generation
- [ ] Go to Saved Ideas page  
- [ ] Click "Start Writing"
- [ ] Verify title is applied and "Change Selection" is hidden
- [ ] Click "Generate Outline" 
- [ ] Verify idea is deleted and project is created
- [ ] Check Saved Ideas page - idea should be gone

#### Test 2: Navigation Safety  
- [ ] Save an idea from title generation
- [ ] Go to Saved Ideas page
- [ ] Click "Start Writing"  
- [ ] Navigate away (e.g., to Home page)
- [ ] Return to Saved Ideas page
- [ ] Verify idea still exists (not deleted)

#### Test 3: UI Context Verification
- [ ] Start new project: "Change Selection" should be visible
- [ ] Continue from saved idea: "Change Selection" should be hidden
- [ ] Continue existing project: "Change Selection" should be hidden

### Key Files Modified:
- `SavedIdeasPage.tsx`: Remove immediate deletion in handleStartWriting  
- `AppPage.tsx`: Add deletion logic to handleGenerateOutline
- `AppliedTitleDisplay.tsx`: Already has proper showChangeSelection logic

### localStorage Data Structure:
```json
{
  "selectedTitle": "BookTitle object",
  "originalTopic": "string", 
  "workflowStep": "title-selection",
  "fromSavedIdea": true,
  "savedIdeaId": "firestore_document_id"  // Added for deletion
}
```