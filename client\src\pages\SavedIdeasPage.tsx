import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { useAppData } from "@/context/AppDataContext";
import { SavedIdeasService } from "@/lib/savedIdeasService";
import { SavedIdea } from "@shared/types";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { LightbulbIcon, CalendarIcon, TrashIcon, PlayIcon, BookIcon, UserIcon, TypeIcon } from "lucide-react";
import { useLocation } from "wouter";
import { format } from "date-fns";

interface IdeaCardProps {
  idea: SavedIdea;
  onStartWriting: (idea: SavedIdea) => void;
  onDelete: (ideaId: string) => void;
}

function IdeaCard({ idea, onStartWriting, onDelete }: IdeaCardProps) {
  const getChapterSummary = () => {
    const totalSubChapters = idea.bookTitle.subChapterCounts.reduce((a, b) => a + b, 0);
    return `${idea.bookTitle.chapterCount} chapters • ${totalSubChapters} sub-chapters`;
  };

  return (
    <Card className="glass-card hover:shadow-lg transition-all duration-300 h-full flex flex-col">
      <CardHeader className="flex-shrink-0">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold mb-2 line-clamp-2">
              {idea.bookTitle.title}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground line-clamp-2">
              Topic: {idea.originalTopic}
            </CardDescription>
          </div>
          <Badge variant="secondary" className="ml-2">
            <LightbulbIcon className="h-3 w-3 mr-1" />
            Idea
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="flex-grow">
        <div className="space-y-3">
          <div className="flex items-center text-sm text-muted-foreground">
            <CalendarIcon className="h-4 w-4 mr-2 flex-shrink-0" />
            Saved {format(idea.createdAt, 'MMM dd, yyyy')}
          </div>
          
          <div className="flex items-center text-sm text-muted-foreground">
            <BookIcon className="h-4 w-4 mr-2 flex-shrink-0" />
            {getChapterSummary()}
          </div>
          
          {/* Book Parameters */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center">
              <TypeIcon className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="text-muted-foreground">Tone:</span>
              <span className="ml-1 capitalize">{idea.bookTitle.tone}</span>
            </div>
            <div className="flex items-center">
              <UserIcon className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="text-muted-foreground">Style:</span>
              <span className="ml-1 capitalize">{idea.bookTitle.style}</span>
            </div>
            <div className="col-span-1">
              <span className="text-muted-foreground">Audience:</span>
              <span className="ml-1">{idea.bookTitle.targetAudience}</span>
            </div>
            {idea.bookTitle.targetLanguage && (
              <div className="col-span-1">
                <span className="text-muted-foreground">Language:</span>
                <span className="ml-1 uppercase">{idea.bookTitle.targetLanguage}</span>
              </div>
            )}
          </div>
          
          {/* Book Summary */}
          {idea.bookTitle.summary && (
            <div className="pt-2 border-t border-muted/20">
              <p className="text-sm text-muted-foreground line-clamp-3">
                {idea.bookTitle.summary}
              </p>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between mt-auto pt-4">
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Saved Idea</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the idea "{idea.bookTitle.title}"? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => onDelete(idea.id)}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
        
        <Button 
          onClick={() => onStartWriting(idea)} 
          className="ml-auto bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
        >
          <PlayIcon className="h-4 w-4 mr-1" />
          Start Writing
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function SavedIdeasPage() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { setSavedIdeaData, deletedIdeaId, setDeletedIdeaId } = useAppData();
  
  const [ideas, setIdeas] = useState<SavedIdea[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isIndexBuilding, setIsIndexBuilding] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Load saved ideas when component mounts
  useEffect(() => {
    loadIdeas();
  }, []);

  // Listen for deletion notifications and refresh the list
  useEffect(() => {
    if (deletedIdeaId) {
      // Remove the deleted idea from the local state
      setIdeas(prevIdeas => prevIdeas.filter(idea => idea.id !== deletedIdeaId));

      // Clear the deletion notification
      setDeletedIdeaId(null);

      console.log('Removed deleted idea from SavedIdeasPage:', deletedIdeaId);
    }
  }, [deletedIdeaId, setDeletedIdeaId]);

  // Auto-retry when index is building (with exponential backoff)
  useEffect(() => {
    if (isIndexBuilding && retryCount < 3) {
      const delay = Math.min(30000, 10000 * Math.pow(1.5, retryCount)); // 10s, 15s, 22.5s
      console.log(`Auto-retry scheduled in ${delay/1000} seconds (attempt ${retryCount + 1})`);
      
      const timer = setTimeout(() => {
        console.log('Auto-retrying to load ideas...');
        loadIdeas(true);
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [isIndexBuilding, retryCount]);

  const loadIdeas = async (isRetry = false) => {
    if (!user?.uid) return;
    
    try {
      setIsLoading(true);
      setError(null);
      setIsIndexBuilding(false);
      
      if (isRetry) {
        setRetryCount(prev => prev + 1);
      }
      
      const userIdeas = await SavedIdeasService.getUserIdeas(user.uid);
      setIdeas(userIdeas);
      setRetryCount(0); // Reset retry count on success
      
      if (isRetry) {
        toast({
          title: "Success",
          description: "Your saved ideas have been loaded!",
        });
      }
    } catch (err) {
      console.error('Error loading saved ideas:', err);
      
      if (err instanceof Error && err.message === 'DATABASE_BUILDING') {
        setIsIndexBuilding(true);
        setError('Database is initializing. Your saved ideas will be available shortly.');
        
        if (!isRetry) {
          toast({
            title: "Database Initializing",
            description: "Setting up your saved ideas database. This usually takes 2-5 minutes.",
            variant: "default"
          });
        }
      } else {
        setIsIndexBuilding(false);
        setError('Failed to load saved ideas');
        toast({
          title: "Error",
          description: "Failed to load your saved ideas. Please try again.",
          variant: "destructive"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartWriting = async (idea: SavedIdea) => {
    try {
      // Store the idea data for the AppPage to load using context
      const ideaForProject = {
        selectedTitle: idea.bookTitle,
        originalTopic: idea.originalTopic,
        workflowStep: 'title-selection' as const,
        fromSavedIdea: true,
        savedIdeaId: idea.id // Store the idea ID for later deletion
      };

      setSavedIdeaData(ideaForProject);

      // DO NOT delete the idea here - it will be deleted when user generates outline
      // This preserves the idea data throughout the workflow until outline generation begins

      // Navigate to the create-ebook page
      setLocation('/create-ebook');

      toast({
        title: "Starting your book",
        description: `Beginning work on "${idea.bookTitle.title}"`,
      });
    } catch (err) {
      console.error('Error starting writing from saved idea:', err);
      toast({
        title: "Error",
        description: "Failed to start writing. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteIdea = async (ideaId: string) => {
    try {
      await SavedIdeasService.deleteIdea(ideaId);
      setIdeas(ideas.filter(i => i.id !== ideaId));
      
      toast({
        title: "Idea deleted",
        description: "The saved idea has been deleted successfully.",
      });
    } catch (err) {
      console.error('Error deleting saved idea:', err);
      toast({
        title: "Error",
        description: "Failed to delete the saved idea. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="glass-card max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please log in to view your saved ideas.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setLocation('/login')} className="w-full">
              Go to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error && !isIndexBuilding) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <Card className="glass-card max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-destructive">Error</CardTitle>
              <CardDescription>{error}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button onClick={() => loadIdeas()} className="w-full">
                Try Again
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  // Special UI for database building state
  if (isIndexBuilding) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <Card className="glass-card max-w-md mx-auto text-center">
            <CardHeader>
              <div className="flex items-center justify-center mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
              <CardTitle className="text-primary">Setting Up Your Saved Ideas</CardTitle>
              <CardDescription className="mt-2">
                {error || "We're initializing your saved ideas database. This usually takes 2-5 minutes."}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  {retryCount > 0 && (
                    <p>Attempt {retryCount + 1} of 4 • Auto-retrying...</p>
                  )}
                  {retryCount === 0 && (
                    <p>First-time setup in progress...</p>
                  )}
                </div>
                
                <div className="bg-muted/20 p-3 rounded text-xs text-muted-foreground">
                  💡 <strong>What's happening?</strong><br/>
                  We're creating database indexes to make your saved ideas super fast to load!
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              <Button 
                onClick={() => loadIdeas(true)} 
                variant="outline" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    Checking...
                  </>
                ) : (
                  'Check Again Now'
                )}
              </Button>
              <Button 
                onClick={() => setLocation('/create-ebook')} 
                variant="ghost" 
                size="sm"
              >
                Generate New Ideas Instead
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Saved Ideas</h1>
            <p className="text-muted-foreground">
              Your collection of book ideas ready to be turned into full projects
            </p>
          </div>

          {/* Ideas Grid */}
          {ideas.length === 0 ? (
            <Card className="glass-card text-center py-12">
              <CardContent>
                <LightbulbIcon className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <CardTitle className="text-xl mb-2">No Saved Ideas</CardTitle>
                <CardDescription className="mb-6">
                  You haven't saved any book ideas yet. Generate some titles and save the ones you like to see them here.
                </CardDescription>
                <Button onClick={() => setLocation('/create-ebook')}>
                  Generate Book Ideas
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {ideas.map((idea) => (
                <IdeaCard
                  key={idea.id}
                  idea={idea}
                  onStartWriting={handleStartWriting}
                  onDelete={handleDeleteIdea}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}